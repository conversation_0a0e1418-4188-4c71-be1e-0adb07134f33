import asyncio, json, random, re, time
from pathlib import Path
from typing import List, Dict, Any
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright, TimeoutError as PWTimeout
from .config import Cfg
from .logger import logger
from .schemas import LessonMeta

OUT = Path(Cfg.EXPORT_DIR)
(OUT / "skool_pages").mkdir(parents=True, exist_ok=True)
(OUT / "captions").mkdir(parents=True, exist_ok=True)
(OUT / "clean").mkdir(parents=True, exist_ok=True)
(OUT / "tactics").mkdir(parents=True, exist_ok=True)

YOUTUBE_ID_RX = re.compile(r'(?:youtube\.com/embed/|youtu\.be/|watch\?v=)([A-Za-z0-9_-]{6,})')
VIMEO_ID_RX   = re.compile(r'player\.vimeo\.com/video/(\d+)')
WISTIA_RX     = re.compile(r'(?:wistia\.com|fast\.wistia\.net)')

async def _sleep():
    await asyncio.sleep((Cfg.REQUEST_PAUSE_MS + random.randint(0, 250)) / 1000)

def _write(path: Path, text: str):
    path.write_text(text, encoding="utf-8", errors="ignore")

async def _login(page):
    await page.goto("https://www.skool.com/login")
    await page.wait_for_selector('input[type="email"]', timeout=20000)
    await page.fill('input[type="email"]', Cfg.SKOOL_EMAIL)
    await page.fill('input[type="password"]', Cfg.SKOOL_PASSWORD)
    await page.click('button:has-text("Sign in")')
    await page.wait_for_load_state("networkidle", timeout=30000)
    if "login" in page.url.lower():
        raise RuntimeError("Login failed (check creds / 2FA).")

async def _collect_links(page) -> List[str]:
    await page.goto(Cfg.CLASSROOM_URL)
    await page.wait_for_load_state("networkidle")
    last_h = 0
    for _ in range(30):
        await page.mouse.wheel(0, 4000); await _sleep()
        h = await page.evaluate("document.body.scrollHeight")
        if h == last_h: break
        last_h = h
    anchors = await page.query_selector_all("a[href]")
    links = []
    for a in anchors:
        href = await a.get_attribute("href")
        if not href: continue
        if href.startswith("/"):
            href = f"https://www.skool.com{href}"
        if Cfg.SPACE_URL and href.startswith(Cfg.SPACE_URL) and "/classroom/" in href:
            links.append(href.split("?")[0])
        elif not Cfg.SPACE_URL and href.startswith("https://www.skool.com/") and "/classroom/" in href:
            links.append(href.split("?")[0])
    links = sorted(set(links))
    logger.info(f"Found {len(links)} classroom links")
    return links

def _parse_video(html: str) -> Dict[str, str]:
    if m := YOUTUBE_ID_RX.search(html): return {"provider": "youtube", "id": m.group(1)}
    if m := VIMEO_ID_RX.search(html):   return {"provider": "vimeo", "id": m.group(1)}
    if WISTIA_RX.search(html):          return {"provider": "wistia", "id": "unknown"}
    return {"provider": "", "id": ""}

def _title_and_date(html: str) -> Dict[str, str]:
    soup = BeautifulSoup(html, "lxml")
    title = (soup.title.string or "").strip() if soup.title else ""
    created = ""
    for el in soup.find_all(["time","span","div"]):
        txt = " ".join((el.get_text(" ", strip=True) or "").split()).lower()
        if any(m in txt for m in ["2023","2024","2025","am","pm","ago","jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec"]):
            created = el.get_text(" ", strip=True); break
    return {"title": title, "created_at": created}

async def _caption_tracks(page, idx: int):
    tracks = await page.eval_on_selector_all("video track[kind='captions']", "els => els.map(e => e.src)")
    saved = []
    for i, t in enumerate(tracks):
        try:
            if not t: continue
            await page.goto(t)
            c = await page.content()
            fp = OUT / "captions" / f"lesson_{idx}_track_{i}.vtt"
            _write(fp, c)
            saved.append(str(fp))
        except Exception as e:
            logger.warning(f"Caption fetch failed: {e}")
    return saved

async def run_scrape() -> Dict[str, Any]:
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=Cfg.HEADLESS)
        ctx = await browser.new_context()
        page = await ctx.new_page()
        await _login(page)

        links = await _collect_links(page)
        if Cfg.MAX_LESSONS and len(links) > Cfg.MAX_LESSONS:
            links = links[:Cfg.MAX_LESSONS]

        catalog, video_refs = [], []
        for idx, url in enumerate(links, 1):
            meta = LessonMeta(idx=idx, url=url, scraped_at=time.strftime("%Y-%m-%d %H:%M:%S"))
            try:
                await page.goto(url); await page.wait_for_load_state("networkidle")
                html = await page.content()
                _write(OUT / "skool_pages" / f"lesson_{idx}.html", html)

                td = _title_and_date(html)
                meta.title, meta.created_at = td["title"], td["created_at"]
                meta.captions = await _caption_tracks(page, idx)

                v = _parse_video(html)
                meta.video_provider, meta.video_id = v["provider"], v["id"]
                if v["provider"]:
                    video_refs.append({"idx": idx, "url": url, **v})

                catalog.append(meta.model_dump())
                logger.info(f"Scraped lesson {idx}: {meta.title or url}")
                await _sleep()
            except PWTimeout:
                meta.error = "Timeout"; catalog.append(meta.model_dump())
                logger.error(f"Timeout on {url}")
            except Exception as e:
                meta.error = repr(e); catalog.append(meta.model_dump())
                logger.exception(e)

        (OUT / "skool_catalog.json").write_text(json.dumps(catalog, indent=2), encoding="utf-8")
        (OUT / "video_refs.json").write_text(json.dumps(video_refs, indent=2), encoding="utf-8")
        await ctx.close(); await browser.close()
        return {"lessons": len(catalog), "video_refs": len(video_refs)}
