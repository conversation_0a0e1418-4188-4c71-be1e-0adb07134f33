import asyncio, json, random, re, time
from pathlib import Path
from typing import List, Dict, Any
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright
from app.config import Cfg
from app.schemas import LessonMeta

OUT = Path(Cfg.EXPORT_DIR)
(OUT / "skool_pages").mkdir(parents=True, exist_ok=True)
(OUT / "captions").mkdir(parents=True, exist_ok=True)
(OUT / "clean").mkdir(parents=True, exist_ok=True)
(OUT / "tactics").mkdir(parents=True, exist_ok=True)

YOUTUBE_ID_RX = re.compile(r'(?:youtube\.com/embed/|youtu\.be/|watch\?v=)([A-Za-z0-9_-]{6,})')
VIMEO_ID_RX   = re.compile(r'player\.vimeo\.com/video/(\d+)')
WISTIA_RX     = re.compile(r'(?:wistia\.com|fast\.wistia\.net)')

async def _sleep():
    await asyncio.sleep((Cfg.REQUEST_PAUSE_MS + random.randint(0, 250)) / 1000)

def _write(path: Path, text: str):
    path.write_text(text, encoding="utf-8", errors="ignore")

def _title_and_date(html: str) -> Dict[str, str]:
    soup = BeautifulSoup(html, "html.parser")
    title = soup.find("title")
    title_text = title.get_text(strip=True) if title else "Unknown"
    return {"title": title_text, "date": ""}

def _extract_video_refs(html: str) -> List[Dict[str, str]]:
    refs = []
    for yt_match in YOUTUBE_ID_RX.finditer(html):
        refs.append({"provider": "youtube", "id": yt_match.group(1)})
    for vimeo_match in VIMEO_ID_RX.finditer(html):
        refs.append({"provider": "vimeo", "id": vimeo_match.group(1)})
    if WISTIA_RX.search(html):
        refs.append({"provider": "wistia", "id": "detected"})
    return refs

async def wait_for_login(page):
    """Wait for user to log in manually"""
    print("\n" + "="*60)
    print("🔐 MANUAL LOGIN REQUIRED")
    print("="*60)
    print("1. A browser window will open to the Skool login page")
    print("2. Please log in with your credentials")
    print("3. Once logged in successfully, come back here and press Enter")
    print("="*60)
    
    # Open login page
    await page.goto("https://www.skool.com/login")
    await page.wait_for_load_state("networkidle")
    
    input("\n👆 Please log in using the browser window, then press Enter here: ")
    
    # Test if login worked by trying to access classroom
    print("🔍 Checking if login was successful...")
    await page.goto(Cfg.CLASSROOM_URL)
    await page.wait_for_load_state("networkidle")
    
    if "login" in page.url.lower():
        print("❌ Login failed - still redirected to login page")
        print("Please try logging in again...")
        input("Press Enter after successful login: ")
        await page.goto(Cfg.CLASSROOM_URL)
        await page.wait_for_load_state("networkidle")
        
        if "login" in page.url.lower():
            raise RuntimeError("❌ Login verification failed. Please check your credentials.")
    
    print("✅ Login successful! Ready to scrape.")

async def collect_lesson_links(page) -> List[str]:
    """Collect all lesson links from the classroom"""
    print("📚 Collecting lesson links...")
    
    # Make sure we're on the classroom page
    await page.goto(Cfg.CLASSROOM_URL)
    try:
        await page.wait_for_load_state("networkidle", timeout=60000)  # 60 second timeout
    except:
        print("   ⚠️ Page load timeout, continuing anyway...")
        await page.wait_for_load_state("domcontentloaded")
    
    print("📜 Scrolling to load all content...")
    last_height = 0
    scroll_attempts = 0
    max_scrolls = 30
    
    while scroll_attempts < max_scrolls:
        # Scroll down
        await page.mouse.wheel(0, 4000)
        await _sleep()
        
        # Check if page height changed
        current_height = await page.evaluate("document.body.scrollHeight")
        
        if current_height == last_height:
            print(f"✅ Finished loading content after {scroll_attempts + 1} scroll attempts")
            break
            
        last_height = current_height
        scroll_attempts += 1
        
        if scroll_attempts % 5 == 0:
            print(f"   Scroll {scroll_attempts}/{max_scrolls}...")
    
    # Collect all links
    print("🔗 Extracting lesson links...")
    anchors = await page.query_selector_all("a[href]")
    
    links = []
    for anchor in anchors:
        href = await anchor.get_attribute("href")
        if not href:
            continue
            
        # Convert relative URLs to absolute
        if href.startswith("/"):
            href = "https://www.skool.com" + href
            
        # Filter for classroom lesson links
        if "/classroom/" in href and href not in links:
            # Get link text for debugging
            text = await anchor.inner_text()
            links.append(href)
            print(f"   Found: {text.strip()[:50]}...")
    
    print(f"📋 Total lesson links found: {len(links)}")
    return links

async def scrape_lesson(page, idx: int, url: str) -> Dict:
    """Scrape a single lesson"""
    print(f"📖 Scraping lesson {idx}: {url}")
    
    meta = LessonMeta(idx=idx, url=url, scraped_at=time.strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # Navigate to lesson
        await page.goto(url)
        try:
            await page.wait_for_load_state("networkidle", timeout=30000)
        except:
            print(f"      ⚠️ Page load timeout for lesson {idx}, continuing...")
            await page.wait_for_load_state("domcontentloaded")
        
        # Get page content
        html = await page.content()
        
        # Save raw HTML
        html_file = OUT / "skool_pages" / f"lesson_{idx}.html"
        _write(html_file, html)
        
        # Extract title and date
        title_data = _title_and_date(html)
        meta.title = title_data["title"]
        meta.created_at = title_data["date"]
        
        print(f"   📝 Title: {meta.title}")
        
        # Extract video references
        video_refs = _extract_video_refs(html)
        if video_refs:
            print(f"   🎥 Found {len(video_refs)} video(s)")
        
        # Try to extract captions
        captions_saved = 0
        try:
            tracks = await page.query_selector_all("track[kind='captions'], track[kind='subtitles']")
            for i, track in enumerate(tracks):
                src = await track.get_attribute("src")
                if src:
                    if src.startswith("/"):
                        src = "https://www.skool.com" + src
                    try:
                        response = await page.request.get(src)
                        if response.ok:
                            content = await response.text()
                            caption_file = OUT / "captions" / f"lesson_{idx}_track_{i}.vtt"
                            _write(caption_file, content)
                            captions_saved += 1
                    except Exception as e:
                        print(f"      ⚠️ Caption fetch failed: {e}")
        except Exception as e:
            print(f"   ⚠️ Caption extraction error: {e}")
        
        meta.captions_found = captions_saved
        if captions_saved > 0:
            print(f"   💬 Saved {captions_saved} caption file(s)")
        
        return {"meta": meta.model_dump(), "video_refs": [{"lesson_idx": idx, **v} for v in video_refs]}
        
    except Exception as e:
        print(f"   ❌ Error scraping lesson {idx}: {e}")
        meta.error = str(e)
        return {"meta": meta.model_dump(), "video_refs": []}

async def run_login_then_scrape():
    """Main scraping function with manual login"""
    async with async_playwright() as pw:
        # Launch browser (visible for login)
        browser = await pw.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # Step 1: Manual login
            await wait_for_login(page)
            
            # Step 2: Collect lesson links
            links = await collect_lesson_links(page)
            
            if not links:
                print("❌ No lesson links found. Check the classroom URL or page structure.")
                return
            
            # Limit lessons if configured
            if Cfg.MAX_LESSONS and len(links) > Cfg.MAX_LESSONS:
                links = links[:Cfg.MAX_LESSONS]
                print(f"📊 Limited to first {Cfg.MAX_LESSONS} lessons")
            
            # Step 3: Scrape each lesson
            print(f"\n🚀 Starting to scrape {len(links)} lessons...")
            
            catalog = []
            all_video_refs = []
            
            for idx, url in enumerate(links, 1):
                result = await scrape_lesson(page, idx, url)
                catalog.append(result["meta"])
                all_video_refs.extend(result["video_refs"])
                
                # Small delay between lessons
                await _sleep()
            
            # Step 4: Save results
            print("\n💾 Saving results...")
            _write(OUT / "skool_catalog.json", json.dumps(catalog, indent=2))
            _write(OUT / "video_refs.json", json.dumps(all_video_refs, indent=2))
            
            # Summary
            print(f"\n🎉 Scraping Complete!")
            print(f"📁 Lessons scraped: {len(catalog)}")
            print(f"🎥 Videos found: {len(all_video_refs)}")
            print(f"📂 Files saved to: {OUT}")
            
            return {
                "lessons_scraped": len(catalog),
                "videos_found": len(all_video_refs),
                "export_dir": str(OUT)
            }
            
        finally:
            await browser.close()

if __name__ == "__main__":
    print("🎓 Skool Classroom Scraper")
    print("=" * 40)
    result = asyncio.run(run_login_then_scrape())
