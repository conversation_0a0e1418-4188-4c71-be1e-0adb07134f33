# Skool Scraper → Tactics Extractor

Scrape a private Skool classroom you have access to, back up HTML, pull captions when possible, normalize text, and extract machine-readable trading tactics to feed into your trading engine.

## Quickstart
```bash
cp .env.example .env  # fill in temp creds
make install
python -m app.cli     # runs full pipeline
# or run the API
make api
# POST http://127.0.0.1:8000/run
```

Outputs go to `skool_export/`.
