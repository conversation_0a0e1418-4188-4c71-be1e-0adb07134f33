FROM python:3.11-slim

RUN apt-get update && apt-get install -y --no-install-recommends \    libnss3 libx11-xcb1 libxcomposite1 libxdamage1 libxfixes3 libxrandr2 libgbm1 libgtk-3-0 libpango-1.0-0 libxshmfence1 \    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt && \    python -m playwright install --with-deps chromium

COPY app ./app
ENV HEADLESS=true
CMD ["python","-m","app.cli"]
