from fastapi import FastAPI
from pathlib import Path
import asyncio, json
from .scrape_skool import run_scrape
from .pull_youtube_captions import run_pull
from .clean_content import run_clean
from .extract_tactics import run_extract

app = FastAPI(title="Skool Scraper API")

@app.get("/health")
def health():
    return {"ok": True}

@app.post("/run")
async def run_pipeline():
    res = await run_scrape()
    pulled = run_pull()
    run_clean()
    run_extract()
    return {"scrape": res, "yt_captions": pulled}

@app.get("/catalog")
def catalog():
    p = Path("skool_export/skool_catalog.json")
    return json.loads(p.read_text()) if p.exists() else []

@app.get("/tactics")
def tactics():
    tdir = Path("skool_export/tactics")
    out = {}
    for fp in sorted(tdir.glob("lesson_*_tactics.json")):
        out[fp.stem] = json.loads(fp.read_text())
    return out
