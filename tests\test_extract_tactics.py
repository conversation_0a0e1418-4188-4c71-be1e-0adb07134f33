from app.extract_tactics import summarize

def test_summarize_basic():
    text = "Use TTM Squeeze on 15m and daily. Enter on break above prior high. Take profit 50%. Consider credit spreads with high IV rank > 60."
    out = summarize(text)
    assert out["uses_squeeze"]
    assert "15m" in out["timeframes"] and "daily" in out["timeframes"]
    assert out["entries"]
    assert out["exits"]
    assert "credit" in out["strategy_bucket"]
    assert any("60" in s for s in out["iv_threshold_clauses"])
