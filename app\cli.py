import asyncio, json
from pathlib import Path
from .scrape_skool import run_scrape
from .pull_youtube_captions import run_pull
from .clean_content import run_clean
from .extract_tactics import run_extract
from .logger import logger

def main():
    logger.info("1) Scraping Skool…")
    res = asyncio.run(run_scrape())
    (Path("skool_export") / "last_run.json").write_text(json.dumps(res, indent=2))
    logger.info(res)

    logger.info("2) (Optional) Pulling YouTube captions…")
    pulled = run_pull()
    logger.info(f"YouTube captions saved: {pulled}")

    logger.info("3) Cleaning content…")
    run_clean()

    logger.info("4) Extracting tactics…")
    run_extract()
    logger.info("Done.")

if __name__ == "__main__":
    main()
