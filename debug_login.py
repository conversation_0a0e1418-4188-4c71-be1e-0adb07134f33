import asyncio
from playwright.async_api import async_playwright
from app.config import Cfg

async def debug_login():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()

        print("Going to Skool login page...")
        await page.goto("https://www.skool.com/login")
        await page.wait_for_load_state("networkidle")

        print("Filling in credentials...")
        await page.fill('input[type="email"]', Cfg.SKOOL_EMAIL)
        await page.fill('input[type="password"]', Cfg.SKOOL_PASSWORD)

        print("Clicking login button...")
        await page.click('button:has-text("Log in")')

        print("Waiting for navigation...")
        await page.wait_for_load_state("networkidle", timeout=30000)

        print(f"Current URL after login: {page.url}")

        if "login" in page.url.lower():
            print("❌ Still on login page - login failed")

            # Check for error messages
            error_selectors = [
                '.error',
                '.alert',
                '[data-testid*="error"]',
                '.message',
                '.notification'
            ]

            for selector in error_selectors:
                try:
                    error_elements = await page.query_selector_all(selector)
                    for elem in error_elements:
                        text = await elem.inner_text()
                        if text.strip():
                            print(f"Error message found: {text}")
                except:
                    pass

            # Check if 2FA is required
            if await page.query_selector('input[type="text"][placeholder*="code"]') or \
               await page.query_selector('input[placeholder*="verification"]'):
                print("🔐 2FA verification required!")

        else:
            print("✅ Login successful!")
            print(f"Redirected to: {page.url}")

        input("Press Enter to close browser...")
        await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_login())
