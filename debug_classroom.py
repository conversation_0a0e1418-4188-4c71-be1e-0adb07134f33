import asyncio
from playwright.async_api import async_playwright
from app.config import Cfg

async def debug_classroom():
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=False)
        page = await browser.new_page()
        
        print("Going to classroom URL...")
        print(f"URL: {Cfg.CLASSROOM_URL}")
        
        await page.goto(Cfg.CLASSROOM_URL)
        await page.wait_for_load_state("networkidle")
        
        print(f"Current URL: {page.url}")
        print(f"Page title: {await page.title()}")
        
        # Check if we're logged in
        if "login" in page.url.lower():
            print("❌ Redirected to login page - not authenticated")
            input("Please log in manually, then press Enter...")
            await page.goto(Cfg.CLASSROOM_URL)
            await page.wait_for_load_state("networkidle")
        
        print(f"Final URL: {page.url}")
        
        # Look for different types of links
        print("\nAnalyzing page content...")
        
        # Get all links
        all_links = await page.query_selector_all("a[href]")
        print(f"Total links found: {len(all_links)}")
        
        # Categorize links
        classroom_links = []
        other_links = []
        
        for link in all_links:
            href = await link.get_attribute("href")
            text = await link.inner_text()
            
            if href:
                if "/classroom/" in href:
                    classroom_links.append({"href": href, "text": text.strip()})
                else:
                    other_links.append({"href": href, "text": text.strip()})
        
        print(f"\nClassroom links found: {len(classroom_links)}")
        for i, link in enumerate(classroom_links[:10]):  # Show first 10
            print(f"  {i+1}. {link['text'][:50]}... -> {link['href']}")
        
        print(f"\nOther links (first 10): {len(other_links)}")
        for i, link in enumerate(other_links[:10]):
            print(f"  {i+1}. {link['text'][:50]}... -> {link['href']}")
        
        # Look for lesson-like content
        print("\nLooking for lesson-like elements...")
        
        # Common selectors for lessons/posts
        selectors = [
            "article",
            ".post",
            ".lesson", 
            ".content",
            "[data-testid*='post']",
            "[data-testid*='lesson']",
            ".feed-item",
            ".classroom-item"
        ]
        
        for selector in selectors:
            elements = await page.query_selector_all(selector)
            if elements:
                print(f"Found {len(elements)} elements with selector: {selector}")
        
        # Save page HTML for inspection
        html = await page.content()
        with open("classroom_debug.html", "w", encoding="utf-8") as f:
            f.write(html)
        print(f"\nPage HTML saved to: classroom_debug.html")
        
        input("Press Enter to close browser...")
        await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_classroom())
