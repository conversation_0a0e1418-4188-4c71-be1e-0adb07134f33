import asyncio, json, random, re, time
from pathlib import Path
from typing import List, Dict, Any
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright
from app.config import Cfg
from app.schemas import LessonMeta

OUT = Path(Cfg.EXPORT_DIR)
(OUT / "skool_pages").mkdir(parents=True, exist_ok=True)
(OUT / "captions").mkdir(parents=True, exist_ok=True)
(OUT / "clean").mkdir(parents=True, exist_ok=True)
(OUT / "tactics").mkdir(parents=True, exist_ok=True)

YOUTUBE_ID_RX = re.compile(r'(?:youtube\.com/embed/|youtu\.be/|watch\?v=)([A-Za-z0-9_-]{6,})')
VIMEO_ID_RX   = re.compile(r'player\.vimeo\.com/video/(\d+)')
WISTIA_RX     = re.compile(r'(?:wistia\.com|fast\.wistia\.net)')

async def _sleep():
    await asyncio.sleep((Cfg.REQUEST_PAUSE_MS + random.randint(0, 250)) / 1000)

def _write(path: Path, text: str):
    path.write_text(text, encoding="utf-8", errors="ignore")

def _title_and_date(html: str) -> Dict[str, str]:
    soup = BeautifulSoup(html, "html.parser")
    title = soup.find("title")
    title_text = title.get_text(strip=True) if title else "Unknown"
    return {"title": title_text, "date": ""}

def _extract_video_refs(html: str) -> List[Dict[str, str]]:
    refs = []
    for yt_match in YOUTUBE_ID_RX.finditer(html):
        refs.append({"provider": "youtube", "id": yt_match.group(1)})
    for vimeo_match in VIMEO_ID_RX.finditer(html):
        refs.append({"provider": "vimeo", "id": vimeo_match.group(1)})
    if WISTIA_RX.search(html):
        refs.append({"provider": "wistia", "id": "detected"})
    return refs

async def run_simple_scrape():
    """Simple scraper that assumes you're already logged in"""
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=False)
        ctx = await browser.new_context()
        page = await ctx.new_page()
        
        print("Going directly to classroom...")
        await page.goto(Cfg.CLASSROOM_URL)
        await page.wait_for_load_state("networkidle")
        
        # Check if we're logged in
        if "login" in page.url.lower():
            print("❌ Not logged in. Please log in manually first.")
            print("Opening login page...")
            await page.goto("https://www.skool.com/login")
            input("Please log in manually, then press Enter to continue...")
            
            # Try classroom again
            await page.goto(Cfg.CLASSROOM_URL)
            await page.wait_for_load_state("networkidle")
            
            if "login" in page.url.lower():
                print("❌ Still not logged in. Exiting.")
                await browser.close()
                return
        
        print("✅ Successfully accessed classroom!")
        print("Scrolling to load all lessons...")
        
        # Scroll to load all content
        last_h = 0
        for i in range(30):
            await page.mouse.wheel(0, 4000)
            await _sleep()
            h = await page.evaluate("document.body.scrollHeight")
            if h == last_h: 
                print(f"Finished scrolling after {i+1} attempts")
                break
            last_h = h
            if i % 5 == 0:
                print(f"Scroll attempt {i+1}/30...")
        
        print("Collecting lesson links...")
        anchors = await page.query_selector_all("a[href]")
        links = []
        for a in anchors:
            href = await a.get_attribute("href")
            if not href: continue
            if href.startswith("/"):
                href = "https://www.skool.com" + href
            if "/classroom/" in href and href not in links:
                links.append(href)
        
        print(f"Found {len(links)} lesson links")
        
        if Cfg.MAX_LESSONS and len(links) > Cfg.MAX_LESSONS:
            links = links[:Cfg.MAX_LESSONS]
            print(f"Limited to first {Cfg.MAX_LESSONS} lessons")

        catalog, video_refs = [], []
        print(f"\nScraping {len(links)} lessons...")
        
        for idx, url in enumerate(links, 1):
            print(f"Scraping lesson {idx}/{len(links)}")
            meta = LessonMeta(idx=idx, url=url, scraped_at=time.strftime("%Y-%m-%d %H:%M:%S"))
            try:
                await page.goto(url)
                await page.wait_for_load_state("networkidle")
                html = await page.content()
                _write(OUT / "skool_pages" / f"lesson_{idx}.html", html)

                td = _title_and_date(html)
                meta.title, meta.date = td["title"], td["date"]
                print(f"  Title: {meta.title}")

                vids = _extract_video_refs(html)
                if vids:
                    video_refs.extend([{"lesson_idx": idx, **v} for v in vids])
                    print(f"  Found {len(vids)} video(s)")

                # Try to get captions
                try:
                    tracks = await page.query_selector_all("track[kind='captions'], track[kind='subtitles']")
                    captions_saved = 0
                    for i, track in enumerate(tracks):
                        src = await track.get_attribute("src")
                        if src:
                            if src.startswith("/"):
                                src = "https://www.skool.com" + src
                            try:
                                response = await page.request.get(src)
                                if response.ok:
                                    content = await response.text()
                                    caption_file = OUT / "captions" / f"lesson_{idx}_track_{i}.vtt"
                                    _write(caption_file, content)
                                    captions_saved += 1
                            except Exception as e:
                                print(f"    Caption fetch failed: {e}")
                    
                    meta.captions_found = captions_saved
                    if captions_saved > 0:
                        print(f"  Saved {captions_saved} caption file(s)")
                        
                except Exception as e:
                    print(f"  Caption extraction error: {e}")

                catalog.append(meta.dict())
                await _sleep()

            except Exception as e:
                print(f"  ❌ Error: {e}")
                meta.error = str(e)
                catalog.append(meta.dict())

        await browser.close()

        # Save results
        _write(OUT / "skool_catalog.json", json.dumps(catalog, indent=2))
        _write(OUT / "video_refs.json", json.dumps(video_refs, indent=2))

        print(f"\n✅ Scraping complete!")
        print(f"📁 Lessons scraped: {len(catalog)}")
        print(f"🎥 Videos found: {len(video_refs)}")
        print(f"📂 Files saved to: {OUT}")
        
        return {
            "lessons_scraped": len(catalog),
            "videos_found": len(video_refs),
            "export_dir": str(OUT)
        }

if __name__ == "__main__":
    print("Starting simple Skool scraper...")
    result = asyncio.run(run_simple_scrape())
