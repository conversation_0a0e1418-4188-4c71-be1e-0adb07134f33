import re
from pathlib import Path
from bs4 import BeautifulSoup

EXPORT = Path("skool_export")
CAPS   = EXPORT / "captions"
PAGES  = EXPORT / "skool_pages"
CLEAN  = EXPORT / "clean"
CLEAN.mkdir(parents=True, exist_ok=True)

VTT_TS = re.compile(r"^\d\d:\d\d:\d\d\.\d{3} --> .*")
SRT_IDX= re.compile(r"^\d+$")
TS_ANY = re.compile(r"\b\d{1,2}:\d{2}:\d{2}\b")

def normalize_text(text: str) -> str:
    text = text.replace("\u200b"," ").replace("\xa0"," ")
    out = []
    for ln in text.splitlines():
        t = ln.strip()
        if VTT_TS.match(t): continue
        if SRT_IDX.match(t): continue
        t = TS_ANY.sub("", t)
        if t: out.append(t)
    return re.sub(r"\s+"," "," ".join(out)).strip()

def html_to_text(fp: Path) -> str:
    html = fp.read_text(encoding="utf-8", errors="ignore")
    soup = BeautifulSoup(html, "lxml")
    for tag in soup(["script","style","noscript"]): tag.decompose()
    return normalize_text(soup.get_text(" ", strip=True))

def run_clean():
    for html in sorted(PAGES.glob("lesson_*.html")):
        idx = html.stem.split("_")[1]
        outp = CLEAN / f"lesson_{idx}.txt"
        if outp.exists(): continue

        tracks = sorted((EXPORT / "captions").glob(f"lesson_{idx}_track_*.vtt"))
        if tracks:
            text = " ".join(normalize_text(t.read_text(encoding="utf-8", errors="ignore")) for t in tracks)
        else:
            text = html_to_text(html)

        outp.write_text(text, encoding="utf-8")

if __name__ == "__main__":
    run_clean()
