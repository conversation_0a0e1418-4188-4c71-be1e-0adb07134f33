import json
from pathlib import Path
from youtube_transcript_api import YouTubeTranscriptApi, TranscriptsDisabled, NoTranscriptFound

def save_txt(vid: str, items, out_dir: Path):
    out_dir.mkdir(parents=True, exist_ok=True)
    lines = [i.get("text","").replace("\n"," ").strip() for i in items]
    (out_dir / f"{vid}.txt").write_text("\n".join([t for t in lines if t]), encoding="utf-8")

def run_pull(refs="skool_export/video_refs.json", out_dir="skool_export/captions") -> int:
    p = Path(refs)
    if not p.exists():
        return 0
    vr = json.loads(p.read_text(encoding="utf-8"))
    saved = 0
    for v in vr:
        if v.get("provider") != "youtube": continue
        vid = v.get("id")
        if not vid: continue
        try:
            try:
                transcript = YouTubeTranscriptApi.get_transcript(vid, languages=['en'])
            except (TranscriptsDisabled, NoTranscriptFound):
                lst = YouTubeTranscriptApi.list_transcripts(vid)
                first = next(iter(lst), None)
                transcript = first.fetch() if first else None
            if transcript:
                save_txt(vid, transcript, Path(out_dir)); saved += 1
        except Exception:
            pass
    return saved

if __name__ == "__main__":
    print(run_pull())
