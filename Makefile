.PHONY: install run api docker-build docker-run

install:
	pip install -r requirements.txt
	python -m playwright install

run:
	python -m app.cli

api:
	uvicorn app.api:app --host 0.0.0.0 --port 8000 --reload

docker-build:
	docker build -t skool-scraper:latest .

docker-run:
	docker run --rm -it \	  -e SKOOL_EMAIL="$${SKOOL_EMAIL}" \	  -e SKOOL_PASSWORD="$${SKOOL_PASSWORD}" \	  -e SKOOL_CLASSROOM_URL="$${SKOOL_CLASSROOM_URL}" \	  -e SKOOL_SPACE_URL="$${SKOOL_SPACE_URL}" \	  -e HEADLESS="true" \	  -v $$(pwd)/skool_export:/app/skool_export \	  skool-scraper:latest
