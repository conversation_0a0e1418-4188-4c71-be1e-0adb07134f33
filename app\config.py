import os
from dotenv import load_dotenv
load_dotenv()

class Cfg:
    SKOOL_EMAIL = os.getenv("SKOOL_EMAIL", "")
    SKOOL_PASSWORD = os.getenv("SKOOL_PASSWORD", "")
    CLASSROOM_URL = os.getenv("SKOOL_CLASSROOM_URL", "")
    SPACE_URL = os.getenv("SKOOL_SPACE_URL", "")
    HEADLESS = os.getenv("HEADLESS", "true").lower() == "true"
    MAX_LESSONS = int(os.getenv("MAX_LESSONS", "0"))
    REQUEST_PAUSE_MS = int(os.getenv("REQUEST_PAUSE_MS", "400"))
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    EXPORT_DIR = "skool_export"
    LOGS_DIR = "logs"
