import re, orjson
from pathlib import Path
from typing import Dict, List

EXPORT = Path("skool_export")
CLEAN  = EXPORT / "clean"
TACTS  = EXPORT / "tactics"
TACTS.mkdir(parents=True, exist_ok=True)

RX = {
    "timeframes": re.compile(r"\b(1m|3m|5m|15m|30m|45m|60m|1h|2h|4h|daily|weekly)\b", re.I),
    "squeeze": re.compile(r"\b(ttm\s*squeeze|squeeze release|bollinger|keltner|compression)\b", re.I),
    "momentum": re.compile(r"\b(histogram|macd|rsi|ema\s*(8|21|34)|momentum)\b", re.I),
    "iv_thresh": re.compile(r"\b(ivp|ivr|iv rank|iv percentile)\s*(<|>|<=|>=|=)?\s*(\d+)", re.I),
    "earnings": re.compile(r"\b(earnings|post-earnings|pre-earnings|iv crush|event risk)\b", re.I),
    "entries": re.compile(r"\benter\b.*?(above|below|break|cross|confirm|close)\b.*?(high|low|ema|swing|range|level)", re.I),
    "exits": re.compile(r"\b(exit|take profit|tp|profit target|stop|time stop|trailing)\b.*?(\d+%|\d+\s*(r|rr))?", re.I),
    "credit": re.compile(r"\b(credit spread|short put|short call|iron condor|strangle|straddle)\b", re.I),
    "debit": re.compile(r"\b(debit spread|call debit|put debit|diagonal|calendar|butterfly|broken wing)\b", re.I),
    "risk": re.compile(r"\b(kelly|max loss|risk per trade|portfolio|var|gamma|delta|theta)\b", re.I),
}

def find_all(rx, text) -> List[str]:
    return sorted(set(m.group(0).strip() for m in rx.finditer(text)))

def summarize(text: str) -> Dict:
    tfs = find_all(RX["timeframes"], text)
    data = {
        "timeframes": tfs,
        "uses_squeeze": bool(RX["squeeze"].search(text)),
        "uses_momentum": bool(RX["momentum"].search(text)),
        "mentions_earnings": bool(RX["earnings"].search(text)),
        "iv_threshold_clauses": find_all(RX["iv_thresh"], text),
        "entries": find_all(RX["entries"], text)[:8],
        "exits": find_all(RX["exits"], text)[:8],
        "strategy_bucket": [],
        "risk_mentions": bool(RX["risk"].search(text)),
    }
    if RX["credit"].search(text): data["strategy_bucket"].append("credit")
    if RX["debit"].search(text):  data["strategy_bucket"].append("debit")
    if not data["strategy_bucket"] and data["mentions_earnings"]: data["strategy_bucket"] = ["event"]
    if not data["strategy_bucket"]: data["strategy_bucket"] = ["unspecified"]
    return data

def run_extract():
    for fp in sorted(CLEAN.glob("lesson_*.txt")):
        idx = int(fp.stem.split("_")[1])
        text = fp.read_text(encoding="utf-8", errors="ignore")
        out = summarize(text)
        (TACTS / f"lesson_{idx}_tactics.json").write_bytes(orjson.dumps(out, option=orjson.OPT_INDENT_2))

if __name__ == "__main__":
    run_extract()
